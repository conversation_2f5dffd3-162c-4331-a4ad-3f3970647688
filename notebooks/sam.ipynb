import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import cv2
import torch.nn as nn
from torch.utils.data import Dataset, DataLoader
import torch.optim as optim
from segment_anything import sam_model_registry, SamPredictor
from segment_anything.utils.transforms import ResizeLongestSide
import albumentations as A
from albumentations.pytorch import ToTensorV2
import os
from tqdm import tqdm
import requests
import warnings
warnings.filterwarnings('ignore')

# Configuration
class Config:
    def __init__(self):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_type = "vit_b"  # or "vit_l", "vit_h" for larger models
        self.checkpoint_dir = "checkpoints"
        self.learning_rate = 1e-4
        self.batch_size = 2  # Reduced for stability
        self.num_epochs = 30
        self.num_workers = 2
        self.image_size = 1024
        self.mask_size = 256
        
        # Create checkpoints directory
        os.makedirs(self.checkpoint_dir, exist_ok=True)

config = Config()

# Download SAM weights automatically
def download_sam_weights(model_type="vit_b"):
    model_urls = {
        "vit_b": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_b_01ec64.pth",
        "vit_l": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_l_0b3195.pth",
        "vit_h": "https://dl.fbaipublicfiles.com/segment_anything/sam_vit_h_4b8939.pth"
    }
    
    checkpoint_filenames = {
        "vit_b": "sam_vit_b_01ec64.pth",
        "vit_l": "sam_vit_l_0b3195.pth", 
        "vit_h": "sam_vit_h_4b8939.pth"
    }
    
    filename = checkpoint_filenames[model_type]
    checkpoint_path = os.path.join(config.checkpoint_dir, filename)
    
    if not os.path.exists(checkpoint_path):
        print(f"Downloading SAM {model_type} weights...")
        url = model_urls[model_type]
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            block_size = 8192
            
            with open(checkpoint_path, 'wb') as file, tqdm(
                desc=filename,
                total=total_size,
                unit='iB',
                unit_scale=True,
                unit_divisor=1024,
            ) as bar:
                for data in response.iter_content(block_size):
                    size = file.write(data)
                    bar.update(size)
                    
            print(f"Downloaded {filename} successfully!")
            
        except Exception as e:
            print(f"Error downloading weights: {e}")
            return None
    
    return checkpoint_path



# Custom Dataset Class
class TreeCanopyDataset(Dataset):
    def __init__(self, annotation_data, image_dir, transform=None, training=True):
        self.image_dir = image_dir
        self.transform = transform
        self.training = training
        self.images = annotation_data['images']
        self.sam_transform = ResizeLongestSide(config.image_size)
        
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        try:
            image_info = self.images[idx]
            image_path = os.path.join(self.image_dir, image_info['file_name'])
            
            # Load image (create synthetic data if file doesn't exist)
            if not os.path.exists(image_path):
                print(f"Warning: Image {image_path} not found. Creating synthetic data.")
                image = self.create_synthetic_image(image_info['width'], image_info['height'])
            else:
                image = cv2.imread(image_path)
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            original_size = image.shape[:2]
            
            # Prepare masks from annotations
            masks = []
            for ann in image_info['annotations']:
                if ann['class'] == 'individual_tree':
                    segmentation = ann['segmentation']
                    mask = self.polygons_to_mask(segmentation, original_size)
                    masks.append(mask)
            
            if masks:
                combined_mask = np.sum(masks, axis=0) > 0
                combined_mask = combined_mask.astype(np.uint8)
            else:
                combined_mask = np.zeros(original_size, dtype=np.uint8)
            
            # Apply SAM preprocessing
            image = self.sam_transform.apply_image(image)
            image = torch.as_tensor(image).permute(2, 0, 1).contiguous()
            
            # Resize mask
            mask = cv2.resize(combined_mask, (config.mask_size, config.mask_size))
            mask = torch.as_tensor(mask > 0).float()
            
            # Apply additional transforms if specified
            if self.transform:
                augmented = self.transform(image=image.permute(1, 2, 0).numpy(), mask=mask.numpy())
                image = augmented['image'].permute(2, 0, 1)
                mask = augmented['mask']
            
            return {
                'image': image,
                'mask': mask.unsqueeze(0),
                'original_size': original_size,
                'image_path': image_path
            }
        
        except Exception as e:
            print(f"Error loading sample {idx}: {e}")
            # Return a dummy sample
            return self.create_dummy_sample()
    
    def create_synthetic_image(self, width, height):
        """Create synthetic image for testing when real images aren't available"""
        image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        # Add some green patches to simulate trees
        for _ in range(10):
            x, y = np.random.randint(0, width-50), np.random.randint(0, height-50)
            cv2.rectangle(image, (x, y), (x+50, y+50), (0, 128, 0), -1)
        return image
    
    def create_dummy_sample(self):
        """Create dummy sample when loading fails"""
        image = torch.randn(3, config.image_size, config.image_size)
        mask = torch.zeros(1, config.mask_size, config.mask_size)
        return {
            'image': image,
            'mask': mask,
            'original_size': (config.image_size, config.image_size),
            'image_path': 'dummy'
        }
    
    def polygons_to_mask(self, segmentation, image_size):
        """Convert polygon points to binary mask"""
        mask = np.zeros(image_size, dtype=np.uint8)
        points = np.array(segmentation).reshape(-1, 2).astype(np.int32)
        cv2.fillPoly(mask, [points], 1)
        return mask

# Enhanced SAM Model for Tree Canopy Detection
class TreeCanopySAM(nn.Module):
    def __init__(self, sam_model):
        super().__init__()
        self.sam = sam_model
        # Add additional layers for better tree canopy detection
        self.enhanced_mask_decoder = nn.Sequential(
            nn.Conv2d(256, 128, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(128, 64, 3, padding=1),
            nn.ReLU(),
            nn.Conv2d(64, 1, 1)
        )
        
    def forward(self, batched_input, multimask_output=False):
        # Use SAM's image encoder
        input_images = torch.stack([self.sam.preprocess(x['image']) for x in batched_input], dim=0)
        image_embeddings = self.sam.image_encoder(input_images)
        
        # Get sparse embeddings (using center point prompts)
        sparse_embeddings = self.get_sparse_embeddings(batched_input)
        
        # Use SAM's mask decoder with enhanced features
        low_res_masks, iou_predictions = self.sam.mask_decoder(
            image_embeddings=image_embeddings,
            image_pe=self.sam.prompt_encoder.get_dense_pe(),
            sparse_prompt_embeddings=sparse_embeddings,
            dense_prompt_embeddings=None,
            multimask_output=multimask_output,
        )
        
        # Apply enhanced mask decoder
        enhanced_masks = self.enhanced_mask_decoder(low_res_masks)
        
        # Upscale masks to original size
        masks = self.sam.postprocess_masks(enhanced_masks, (1024, 1024), (1024, 1024))
        
        return masks, iou_predictions
    
    def get_sparse_embeddings(self, batched_input):
        """Generate sparse embeddings using center points of images as prompts"""
        points = []
        labels = []
        
        for input_data in batched_input:
            # Use image center as prompt point
            h, w = input_data['image'].shape[-2:]
            points.append([[w//2, h//2]])  # Center point
            labels.append([1])  # Foreground label
        
        points = torch.tensor(points, device=self.sam.device)
        labels = torch.tensor(labels, device=self.sam.device)
        
        sparse_embeddings, _ = self.sam.prompt_encoder(
            points=(points, labels),
            boxes=None,
            masks=None,
        )
        
        return sparse_embeddings

# Training function
def train_model(model, dataloader, val_dataloader, config):
    model.train()
    model.to(config.device)
    
    # Only train the enhanced decoder initially
    optimizer = optim.Adam(model.enhanced_mask_decoder.parameters(), lr=config.learning_rate)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=3, factor=0.5)
    criterion = nn.BCEWithLogitsLoss()
    
    train_losses = []
    val_losses = []
    best_val_loss = float('inf')
    
    for epoch in range(config.num_epochs):
        model.train()
        epoch_loss = 0
        
        pbar = tqdm(dataloader, desc=f'Epoch {epoch+1}/{config.num_epochs}')
        for batch_idx, batch in enumerate(pbar):
            images = batch['image'].to(config.device)
            masks = batch['mask'].to(config.device)
            
            # Skip dummy samples
            if 'dummy' in batch['image_path'][0]:
                continue
                
            # Create batched input format for SAM
            batched_input = [{'image': img} for img in images]
            
            optimizer.zero_grad()
            
            try:
                # Forward pass
                pred_masks, _ = model(batched_input)
                pred_masks = pred_masks.squeeze(1)
                
                # Resize predictions to match target mask size
                pred_masks = torch.nn.functional.interpolate(
                    pred_masks.unsqueeze(1), 
                    size=masks.shape[-2:], 
                    mode='bilinear', 
                    align_corners=False
                ).squeeze(1)
                
                loss = criterion(pred_masks, masks.squeeze(1))
                
                # Only backpropagate if loss is finite
                if torch.isfinite(loss):
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                    optimizer.step()
                    epoch_loss += loss.item()
                    pbar.set_postfix({'loss': loss.item()})
                else:
                    print(f"Warning: Non-finite loss at batch {batch_idx}")
                    
            except Exception as e:
                print(f"Error in training batch {batch_idx}: {e}")
                continue
        
        if len(dataloader) > 0:
            avg_train_loss = epoch_loss / len(dataloader)
        else:
            avg_train_loss = epoch_loss
            
        train_losses.append(avg_train_loss)
        
        # Validation
        val_loss = validate_model(model, val_dataloader, criterion, config)
        val_losses.append(val_loss)
        
        scheduler.step(val_loss)
        
        print(f'Epoch {epoch+1}/{config.num_epochs}, Train Loss: {avg_train_loss:.4f}, Val Loss: {val_loss:.4f}')
        
        # Save best model
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': avg_train_loss,
                'val_loss': val_loss,
            }, os.path.join(config.checkpoint_dir, 'best_tree_canopy_sam.pth'))
            print(f"Saved best model with val loss: {val_loss:.4f}")
    
    return train_losses, val_losses

def validate_model(model, dataloader, criterion, config):
    model.eval()
    total_loss = 0
    num_batches = 0
    
    with torch.no_grad():
        for batch in dataloader:
            images = batch['image'].to(config.device)
            masks = batch['mask'].to(config.device)
            
            # Skip dummy samples
            if 'dummy' in batch['image_path'][0]:
                continue
                
            batched_input = [{'image': img} for img in images]
            
            try:
                pred_masks, _ = model(batched_input)
                pred_masks = pred_masks.squeeze(1)
                
                pred_masks = torch.nn.functional.interpolate(
                    pred_masks.unsqueeze(1), 
                    size=masks.shape[-2:], 
                    mode='bilinear', 
                    align_corners=False
                ).squeeze(1)
                
                loss = criterion(pred_masks, masks.squeeze(1))
                
                if torch.isfinite(loss):
                    total_loss += loss.item()
                    num_batches += 1
                    
            except Exception as e:
                print(f"Error in validation: {e}")
                continue
    
    return total_loss / max(num_batches, 1)

# Data transforms
def get_transforms():
    train_transform = A.Compose([
        A.HorizontalFlip(p=0.5),
        A.VerticalFlip(p=0.5),
        A.RandomRotate90(p=0.5),
        A.ShiftScaleRotate(shift_limit=0.05, scale_limit=0.05, rotate_limit=10, p=0.5),
        A.RandomBrightnessContrast(p=0.3),
        A.GaussNoise(var_limit=(10.0, 50.0), p=0.2),
        ToTensorV2(),
    ])
    
    val_transform = A.Compose([
        ToTensorV2(),
    ])
    
    return train_transform, val_transform

# Visualization function
def visualize_prediction(model, dataset, index, device):
    model.eval()
    item = dataset[index]
    image = item['image'].unsqueeze(0).to(device)
    true_mask = item['mask'].squeeze().cpu().numpy()
    
    with torch.no_grad():
        batched_input = [{'image': image.squeeze(0)}]
        pred_mask, _ = model(batched_input)
        pred_mask = torch.sigmoid(pred_mask.squeeze())
        pred_mask = pred_mask.cpu().numpy()
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Original image
    axes[0].imshow(image.squeeze().permute(1, 2, 0).cpu().numpy())
    axes[0].set_title('Original Image')
    axes[0].axis('off')
    
    # True mask
    axes[1].imshow(true_mask, cmap='jet')
    axes[1].set_title('True Mask')
    axes[1].axis('off')
    
    # Predicted mask
    axes[2].imshow(pred_mask, cmap='jet')
    axes[2].set_title('Predicted Mask')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.show()

# Create sample data for testing
def create_sample_data():
    """Create sample annotation data if file doesn't exist"""
    sample_data = {
        "images": [
            {
                "file_name": "sample_image_1.tif",
                "width": 1024,
                "height": 1024,
                "cm_resolution": 10,
                "scene_type": "agriculture_plantation",
                "annotations": [
                    {
                        "class": "individual_tree",
                        "confidence_score": 1.0,
                        "segmentation": [100, 100, 200, 100, 200, 200, 100, 200]
                    }
                ]
            }
        ]
    }
    return sample_data

# Main training script
def main():
    print("Initializing Tree Canopy Detection Training...")
    print(f"Using device: {config.device}")
    
    # Download SAM weights
    print("Downloading SAM weights...")
    checkpoint_path = download_sam_weights(config.model_type)
    
    if checkpoint_path is None:
        print("Failed to download SAM weights. Please download manually.")
        return
    
    # Initialize SAM model
    print("Loading SAM model...")
    try:
        sam = sam_model_registry[config.model_type](checkpoint=checkpoint_path)
        model = TreeCanopySAM(sam)
        print("SAM model loaded successfully!")
    except Exception as e:
        print(f"Error loading SAM model: {e}")
        return
    
    # Load annotation data
    try:
        with open('../data/train_annotations.json', 'r') as f:
            annotation_data = json.load(f)
        print("Loaded annotation data from file.")
    except FileNotFoundError:
        print("Annotation file not found. Using sample data for demonstration.")
        annotation_data = create_sample_data()
    
    # Prepare datasets
    print("Preparing datasets...")
    train_transform, val_transform = get_transforms()
    
    # For demonstration, using the same data for train and val
    # In practice, you should split your data properly
    
    with open('../data/train_annotations.json', 'r') as file:
        data = json.load(file)

    train_dataset = TreeCanopyDataset(
        annotation_data=data,
        image_dir='../data/train_images',  # Update this path
        transform=train_transform,
        training=True
    )
    
    val_dataset = TreeCanopyDataset(
        annotation_data=data,
        image_dir='../data/train_images',  # Update this path
        transform=val_transform,
        training=False
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.batch_size,
        shuffle=True,
        num_workers=config.num_workers
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=config.batch_size,
        shuffle=False,
        num_workers=config.num_workers
    )
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    # Train the model
    print("Starting training...")
    train_losses, val_losses = train_model(model, train_loader, val_loader, config)
    
    # Plot training history
    plt.figure(figsize=(10, 5))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.title('Training History')
    plt.savefig(os.path.join(config.checkpoint_dir, 'training_history.png'))
    plt.show()
    
    # Visualize some predictions
    print("Visualizing predictions...")
    for i in range(min(3, len(val_dataset))):
        visualize_prediction(model, val_dataset, i, config.device)

if __name__ == "__main__":
    main()
    # data/train_annotation_sample.json